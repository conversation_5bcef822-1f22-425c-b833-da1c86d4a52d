<h1 align="center">
 Astro Theme OpenBLOG
</h1>

<div align="center">

<img src="public/project.jpg" alt="Screenshot" />

<hr/>

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fdanielcgilibert%2Fblog-template)
[![Deploy to Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/danielcgilibert/blog-template)

</div>

## 💻 Demo

Échale un vistazo a la [Demo](https://kuberns-blogs.vercel.app/), alojada en Vercel

## ⚙️ Stack

- [**ASTRO** + **Typescript**](https://astro.build/) - Astro es el framework web todo en uno diseñado para la velocidad.
- [**Tailwind CSS** + **Tailwind-Merge** + **clsx**](https://tailwindcss.com/) - Tailwind CSS es un framework de CSS de tipo utility-first.
- [**Tabler Icons**](https://tabler-icons.io/i/) - Iconos SVG de código abierto..

## ✅ Features:

- ✅ Estilo mínimo
- ✅ Compatible con dispositivos móviles
- ✅ Rendimiento 100/100 en Lighthouse
- ✅ Amigable con SEO mediante URLs canónicas y datos OpenGraph
- ✅ Soporte para sitemap
- ✅ Soporte para feeds RSS
- ✅ Soporte para Markdown y MDX
- ✅ Resaltado de sintaxis
- ✅ Optimización de imágenes
- ✅ Tabla de contenidos
- ✅ Modo oscuro
- ✅ Tiempo de lectura
- ✅ [Pagefind](https://pagefind.app/) static search library integration
- ✅ Posts relacionados
- ✅ Compartir posts (Linkedin, twitter)

## 🛣️ Roadmap

- ❌ Botón para copiar código

## 🚀 Getting Started

**Extensiones recomendadas para VSCode:**

- [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss).
- [Astro](https://marketplace.visualstudio.com/items?itemName=astro-build.astro-vscode).

1. Clona o haz un [fork](https://github.com/danielcgilibert/blog-template/fork) del repositorio:

```bash
**************:danielcgilibert/blog-template.git
```

2. Instala las dependencias:

```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Ejecuta el servidor de desarrollo:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

## 🗂️ Estructura del proyecto

```
├── public/
├── src/
│   ├── assets/
│   ├── components/
│   ├── content/
│   ├── layouts/
│   ├── data/
│   ├── utils/
│   ├── styles/
│   └── pages/
├── astro.config.mjs
├── README.md
├── package.json
└── tsconfig.json
```

## 👋 Contribuciones

<a href="https://github.com/danielcgilibert/blog-template/graphs/contributors">
  <img src="https://contrib.rocks/image?repo=danielcgilibert/blog-template" />
</a>
