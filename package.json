{"name": "kuberns-blogs", "type": "module", "version": "1.1.0", "description": "Kuberns Blogs", "author": "<PERSON><PERSON><PERSON>", "license": "GPL-3.0-only", "repository": {"type": "git", "url": "https://github.com/kuberns/kuberns-blogs"}, "keywords": ["kubenrs", "kuberns cloud", "kuberns-blog-template", "kuberns-blog", "kuberns-b"], "bugs": {"url": "https://github.com/kuberns/kuberns-blogs/issues"}, "scripts": {"dev": "npx tinacms dev -c \"astro dev\"", "start": "astro dev", "build": "astro build && npx tinacms build", "tinabuild": "npx tinacms build", "sync": "astro sync", "preview": "astro preview", "postbuild": "pagefind --site dist", "format:check": "prettier --plugin-search-dir=. --check .", "format": "prettier --plugin-search-dir=. --write .", "lint": "eslint .", "pre-commit": "lint-staged", "prepare": "husky install"}, "dependencies": {"@astrojs/react": "^4.2.0", "@astrojs/rss": "4.0.1", "astro": "^5.3.1", "astro-font": "^0.0.72", "jsonwebtoken": "^9.0.2", "react": "^18.3.1", "react-dom": "^18.3.1", "vite": "^6.2.0"}, "devDependencies": {"@astrojs/mdx": "^4.0.8", "@astrojs/sitemap": "3.0.4", "@astrojs/tailwind": "5.1.0", "@pagefind/default-ui": "^1.0.4", "@tailwindcss/typography": "0.5.10", "@tinacms/cli": "^1.5.30", "@types/jsonwebtoken": "^9.0.9", "@typescript-eslint/parser": "^6.16.0", "clsx": "2.0.0", "eslint": "^8.56.0", "eslint-plugin-astro": "^0.31.0", "eslint-plugin-jsx-a11y": "^6.8.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "mdast-util-to-string": "^4.0.0", "motion": "^10.16.4", "pagefind": "^1.0.3", "prettier": "^3.0.3", "prettier-config-standard": "^7.0.0", "prettier-plugin-astro": "^0.12.0", "reading-time": "^1.5.0", "slugify": "^1.6.6", "tailwind-merge": "2.0.0", "tailwindcss": "3.3.5", "tinacms": "^1.5.21", "typescript": "^5.2.2"}, "lint-staged": {"*.{astro,js,jsx,ts,tsx,md,mdx,json}": ["prettier --write --plugin-search-dir=."]}}