---
import BaseLayout from '@/layouts/BaseLayout'
import TitlePage from '@/components/TitlePage'
import { getTags } from '@/utils'
const tags = await getTags()
---

<BaseLayout title='Tags'>
	<TitlePage title='Kuberns' />
	<div class='flex justify-center flex-wrap gap-4'>
		{
			tags.map((tag) => (
				<a
					href={`/tags/${tag}`}
					class='inline-block bg-gray-200  rounded-full px-3 py-1 text-sm font-semibold text-gray-700 mr-2 mb-2'
				>
					#{tag}
				</a>
			))
		}
	</div>
</BaseLayout>
