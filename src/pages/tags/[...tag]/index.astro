---
import BaseLayout from '@/layouts/BaseLayout'
import ListPosts from '@/components/ListPosts'
import TitlePage from '@/components/TitlePage'
import { getTags, getPostByTag } from '@/utils'

export async function getStaticPaths() {
	const tags = await getTags()

	return tags.map((tag) => ({
		params: { tag },
		props: { tag }
	}))
}

const { tag } = Astro.props as { tag: string }

const posts = await getPostByTag(tag)
---

<BaseLayout title={tag}>
	<TitlePage title={'Kuberns'} />
	<ListPosts posts={posts} />
</BaseLayout>
