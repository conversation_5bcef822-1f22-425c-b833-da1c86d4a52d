---
import ListCategories from '@/components/ListCategories'
import ListPosts from '@/components/ListPosts'
import TitlePage from '@/components/TitlePage'
import BaseLayout from '@/layouts/BaseLayout'
import { getPosts } from '@/utils'
import { siteConfig } from '@/site-config'

const MAX_POSTS = 5 // max number of posts to show on the home page
const posts = await getPosts(MAX_POSTS)
---

<BaseLayout title='Home'>
	<TitlePage title={siteConfig.title} />
	<ListCategories />

	<div>
		<h2 class='text-lg font-medium tracking-wide text-end'>Latest Posts</h2>
		<ListPosts FirstBig={true} posts={posts} />
	</div>
</BaseLayout>

<script>
	import { animate } from 'motion'
	const showAnimations = localStorage.getItem('animations') === 'true'

	if (showAnimations) {
		animate(
			'.title',
			{ y: [80, 0], opacity: [0, 1] },
			{
				duration: 2.5,
				opacity: { duration: 3 },

				offset: [0, 0.55, 0.75]
			}
		)
	}
</script>
