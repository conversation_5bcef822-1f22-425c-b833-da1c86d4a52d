---
import BaseLayout from '@/layouts/BaseLayout'
import ListPosts from '@/components/ListPosts'
import ListCategories from '@/components/ListCategories'
import TitlePage from '@/components/TitlePage'
import { sluglify, unsluglify, getCategories, getPosts } from '@/utils'
import { siteConfig } from '@/data/site.config'
import Pagination from '@/components/Pagination'

export async function getStaticPaths({ paginate }: any) {
	const categories = await getCategories()
	const allPosts = await getPosts()

	return categories.flatMap((category: string) => {
		const unsluglifyNameCategory = unsluglify(category!.toLowerCase())
		const filteredPosts = allPosts.filter(
			(post) => post.data.category.toLowerCase() === unsluglifyNameCategory
		)

		return paginate(filteredPosts, {
			params: {
				category: sluglify(category.toLowerCase())
			},
			pageSize: siteConfig.paginationSize
		})
	})
}
const params = Astro.params
const { page } = Astro.props

const unsluglifyNameCategory = unsluglify(params.category!.toLowerCase())
const categoryName = (await getCategories()).find(
	(category) => category.toLowerCase() === unsluglifyNameCategory
)
const posts = page.data
---

<BaseLayout title={categoryName}>
	<TitlePage title={'Kuberns'} />
	<ListCategories activeCategory={params.category} />
	<ListPosts posts={posts} />
	<Pagination page={page} />
</BaseLayout>
