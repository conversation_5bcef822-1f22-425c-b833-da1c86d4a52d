export async function getUserFromRequest(request) {
    const cookieHeader = request.headers.get('cookie');
    
    console.log("🛠 Received Cookies in /admin:", cookieHeader); // Debugging log
  
    if (!cookieHeader) {
      console.log("❌ No cookies received");
      return null;
    }
  
    // 🔥 Debug: Print parsed cookies
    const cookies = Object.fromEntries(
      cookieHeader.split("; ").map(c => c.split("="))
    );
    console.log("🔍 Parsed Cookies:", cookies);
  
    // ✅ Check for correct token
    if (cookies.authToken === 'admin-secret') {
      console.log("✅ User authenticated!");
      return { role: 'admin', name: 'Admin User' };
    }
  
    console.log("❌ Authentication failed");
    return null;
  }
  