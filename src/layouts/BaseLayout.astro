---
import BaseHead from '@/components/BaseHead'
import Header from '@/components/Header'
import Footer from '@/components/Footer'
import ProviderTheme from '@/components/ProviderTheme'
import ProviderAnimations from '@/components/ProviderAnimations'
import TwSizeIndicator from '@/components/TwSizeIndicator'
import EditBlog from '@/components/editBlog'
const { title, description, image, articleDate } = Astro.props
---

<html lang='en' class='scroll-smooth'>
	<head>
		<title>{title} | Kuberns Blog</title>
		<BaseHead title={title} description={description} ogImage={image} articleDate={articleDate} />
		<ProviderTheme />
		<ProviderAnimations />
		<!-- Google tag (gtag.js) -->
		<script async src="https://www.googletagmanager.com/gtag/js?id=G-K5JXYSXWNH"></script>
		<script is:inline>
			window.dataLayer = window.dataLayer || [];
			function gtag(){dataLayer.push(arguments);}
			gtag('js', new Date());

			gtag('config', 'G-K5JXYSXWNH');
		</script>		
	</head>

	<body class='bg-white text-stone-950 dark:bg-[#0a0910] dark:text-white'>
		<main
			class='px-5 sm:mx-auto sm:max-w-2xl sm:px-8 lg:px-0 antialiased md:max-w-6xl grid gap-12 mt-4 overflow-hidden md:overflow-visible'
		>
			<Header />
			<slot />
			<Footer />
		</main>
		<TwSizeIndicator />
		<EditBlog />
		<style>
			body {
				margin-left: calc(100vw - 100%); /* prevent layout shift */
			}
		</style>
	</body>
</html>
