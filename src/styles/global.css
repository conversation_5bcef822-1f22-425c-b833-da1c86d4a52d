@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	/* body {
		transition: background-color 0.5s ease;
	} */

	.prose code {
		@apply text-base;
	}

	/* prose styles here */
	.prose h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		@apply text-zinc-800 dark:text-zinc-100   !important;
	}

	html.dark .shiki,
	html.dark .shiki span {
	  color: var(--shiki-dark) !important;
	  background-color: theme(colors.gray.900) !important;
	}
}

.glass {
	background: rgba(57, 56, 56, 0.52);
	backdrop-filter: blur(13px) saturate(150%);
	-webkit-backdrop-filter: blur(13px) saturate(150%);
	z-index: -1;
}

.shadow {
	box-shadow: -5px 3px 8px 1px rgba(0, 0, 0, 0.12);
}
