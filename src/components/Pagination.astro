---
import ArrowLeft from './icons/ArrowLeft.astro'
import ArrowR<PERSON> from './icons/ArrowRight.astro'

const { page } = Astro.props
---

<div class='flex gap-5 md:gap-1 justify-around md:justify-end'>
	<!-- Previous Button -->
	{
		page.start > 0 && (
			<a
				href={page.url.prev}
				class='flex items-center justify-center px-8 md:px-4 h-10 text-base font-medium text-black bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-transparent dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white'
			>
				<ArrowLeft />
			</a>
		)
	}

	<!-- Next Button -->

	{
		page.currentPage < page.lastPage && (
			<a
				href={page.url.next}
				class='flex items-center justify-center px-8 md:px-4 h-10 ml-3 text-base font-medium text-black bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-transparent dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white'
			>
				<ArrowRight />
			</a>
		)
	}
</div>
