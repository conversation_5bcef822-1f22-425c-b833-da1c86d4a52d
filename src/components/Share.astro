---
import { siteConfig } from '@/data/site.config'
import TwitterColorIcon from './icons/TwitterColorIcon.astro'
import LinkedinColorIcon from './icons/LinkedinColorIcon.astro'
const message = siteConfig.shareMessage

const URL = Astro.url.href
---

<div class='flex flex-col gap-2'>
	<span class='mb-1 font-bold text-2xl'>Share</span>
	<ul class='flex gap-3 text-black dark:text-white'>
		<li>
			<a
				href={`https://twitter.com/intent/tweet?text=${message + ' ' + URL}`}
				aria-label='Share on Twitter'><TwitterColorIcon /></a
			>
		</li>
		<li>
			<a
				href={`https://www.linkedin.com/shareArticle?mini=true&url=${URL}`}
				aria-label='Share on LinkedIn'
			>
				<LinkedinColorIcon /></a
			>
		</li>
	</ul>
</div>
