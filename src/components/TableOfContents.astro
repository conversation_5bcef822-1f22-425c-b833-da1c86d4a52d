---
import TableOfContentsHeading from '@/components/TabletOfContentsHeading'

const { headings } = Astro.props

type TableOfContent = {
	depth: number
	text: string
	slug: string
	subheadings: TableOfContent[]
}

const toc = buildToc(headings)
function buildToc(headings: TableOfContent[]) {
	let toc: TableOfContent[] = []
	let parentHeadings = new Map<number, TableOfContent>()

	headings.forEach((h) => {
		let heading = { ...h, subheadings: [] }
		parentHeadings.set(heading.depth, heading)

		if (heading.depth === 1 || heading.depth === 2) {
			toc.push(heading)
		} else {
			const parent = parentHeadings.get(heading.depth - 1)
			if (parent) {
				parent.subheadings.push(heading)
			} else {
				// Optional: fall back to root TOC if no parent found
				toc.push(heading)
			}
		}
	})
	return toc
}
---

<nav class='max-w-xs dark:text-black'>
	<p class='font-bold mb-3 text-2xl dark:text-white'>Index</p>
	<ul class='[text-wrap:balance] flex flex-col gap-1'>
		{toc.map((heading) => <TableOfContentsHeading heading={heading} />)}
	</ul>
</nav>
