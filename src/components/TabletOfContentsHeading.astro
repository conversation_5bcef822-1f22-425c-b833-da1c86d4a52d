---
import { cn } from '@/utils'
const { heading } = Astro.props

type Heading = {
	depth: number
	text: string
	slug: string
	subheadings: Heading[]
}

export interface Props {
	heading: Heading
}
---

<li class='flex flex-col'>
	<a
		href={'#' + heading.slug}
		class={cn(
			`bg-slate-200 dark:bg-slate-800 dark:hover:bg-[#2758D1] hover:bg-[#2758D1] hover:text-white  py-1 px-4 dark:text-white rounded-full mb-2 first-letter:uppercase w-fit line-clamp-2`
		)}
	>
		{heading.text}
	</a>
	{
		heading.subheadings.length > 0 && (
			<ul class='ml-3'>
				{heading.subheadings.map((subheading) => (
					<Astro.self heading={subheading} />
				))}
			</ul>
		)
	}
</li>
