---
import HeaderLink from '@/components/HeaderLink'
import MenuIcon from './icons/MenuIcon.astro'
import Search from '@/components/Search'
import TagIcon from './icons/TagIcon.astro'
import ToggleTheme from './ToggleTheme.astro'
import { SOCIALNETWORKS } from '@/data/links'
---

<header class='relative flex items-center h-12 font-semibold'>
	<a class='text-lg mr-auto' href='/'>Home</a>

	<div
		id='astro-header-drawer'
		class='shadow rounded-l-lg md:bg-transparent dark:md:bg-transparent bg-white dark:bg-[#0a0910] md:shadow-none md:rounded-none md:border-none md:h-auto md:static absolute transition-transform duration-300 ease-in translate-x-96 md:translate-x-0 top-12 -right-5 pl-4 pt-6 pb-4 md:p-0 h-[200px] w-[200px] z-50'
	>
		<nav
			class='flex h-full flex-col justify-between gap-12 text-left md:flex-row md:w-full md:gap-5'
		>
			<div
				class='flex flex-col gap-4 md:flex-row md:border-r-2 border-black pr-4 dark:border-white'
			>
				<HeaderLink href='/tags' class='flex items-center gap-1 text-2xl md:text-base'>
					<TagIcon /> Tags
				</HeaderLink>
			</div>

			<div class='flex justify-center items-center md:justify-end gap-3 md:p-0'>
				{
					SOCIALNETWORKS.map((network) => (
						<HeaderLink class='' href={network.url} target='_blank' aria-label={network.name}>
							<span>{<network.icon />} </span>
						</HeaderLink>
					))
				}
			</div>
		</nav>
	</div>

	<div class='flex items-center gap-3 md:pl-8' transition:persist='navbar'>
		<div>
			<Search />
		</div>
		<ToggleTheme />
		<button id='astro-header-drawer-button' type='button' class='md:ml-6 md:hidden'>
			<MenuIcon />
			<span class='sr-only'>Show Menu</span>
		</button>
	</div>
</header>
<script>
	document.addEventListener('click', (event) => {
		const menu = document.getElementById('astro-header-drawer')
		const menuButton = document.getElementById('astro-header-drawer-button')
		const isClickInside =
			menu?.contains(event.target as HTMLDivElement) ||
			menuButton?.contains(event.target as HTMLButtonElement)

		if (isClickInside) {
			menu?.classList.toggle('translate-x-96')
		} else {
			menu?.classList.add('translate-x-96')
		}
	})
</script>
