---
import { siteConfig } from '@/site-config'
import '../styles/global.css'
import { ViewTransitions } from 'astro:transitions'

export interface Props {
	title: string
	description?: string
	ogImage?: string | undefined
	articleDate?: string | undefined
}

const { title, description = siteConfig.description, ogImage, articleDate } = Astro.props
const canonicalURL = new URL(Astro.url.pathname, Astro.site)
const socialImageURL = new URL(ogImage ? ogImage : '/open-graph.png', Astro.url).href

const titleSeparator = '•'

const siteTitle = `${title} ${titleSeparator} ${siteConfig.title}`

import { join } from 'path'
import { AstroFont } from 'astro-font'
---

<!-- Global Metadata -->
<meta charset='utf-8' />
<meta name='viewport' content='width=device-width,initial-scale=1' />
<link rel='icon' type='image/svg+xml' href='/favicon.svg' />
<meta name='generator' content={Astro.generator} />

<!-- Canonical URL -->
<link rel='canonical' href={canonicalURL} />

<!-- Primary Meta Tags -->
<title>{"Kuberns Blog"}</title>

<!-- ViewTransitions  -->
<ViewTransitions />

<!-- SEO -->
<meta name='title' content={siteTitle} />
<meta name='description' content={description} />
<meta name='author' content={siteConfig.author} />

<!-- Open Graph / Facebook -->
<meta property='og:type' content={articleDate ? 'article' : 'website'} />
<meta property='og:url' content={Astro.url} />
<meta property='og:title' content={title} />
<meta property='og:description' content={description} />
<meta property='og:image' content={socialImageURL} />
{
	articleDate && (
		<>
			<meta property='article:author' content={siteConfig.author} />
			<meta property='article:published_time' content={articleDate} />
		</>
	)
}

<!-- Twitter -->
<meta property='twitter:card' content='summary_large_image' />
<meta property='twitter:url' content={Astro.url} />
<meta property='twitter:title' content={title} />
<meta property='twitter:description' content={description} />
<meta property='twitter:image' content={socialImageURL} />

<!-- RSS auto-discovery -->
<link rel='alternate' type='application/rss+xml' title={siteConfig.title} href='/rss.xml' />

<AstroFont
	config={[
		{
			preload: false,
			display: 'swap',
			name: 'Manrope',
			fallback: 'sans-serif',
			src: [
				{
					weight: '200',
					style: 'normal',
					path: join(process.cwd(), 'public', 'fonts', 'Manrope-ExtraLight.woff2')
				},
				{
					weight: '300',
					style: 'normal',
					path: join(process.cwd(), 'public', 'fonts', 'Manrope-Light.woff2')
				},
				{
					weight: '400',
					style: 'normal',
					path: join(process.cwd(), 'public', 'fonts', 'Manrope-Regular.woff2')
				},
				{
					weight: '500',
					style: 'normal',
					path: join(process.cwd(), 'public', 'fonts', 'Manrope-Medium.woff2')
				},
				{
					weight: '600',
					style: 'normal',
					path: join(process.cwd(), 'public', 'fonts', 'Manrope-SemiBold.woff2')
				},
				{
					weight: '700',
					preload: true,
					style: 'normal',
					path: join(process.cwd(), 'public', 'fonts', 'Manrope-Bold.woff2')
				},
				{
					weight: '800',
					style: 'normal',
					path: join(process.cwd(), 'public', 'fonts', 'Manrope-ExtraBold.woff2')
				}
			]
		}
	]}
/>
