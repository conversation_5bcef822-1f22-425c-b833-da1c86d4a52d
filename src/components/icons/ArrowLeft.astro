---
const { width = 24, height = 24 } = Astro.props
---

<svg
	xmlns='http://www.w3.org/2000/svg'
	class='icon icon-tabler icon-tabler-circle-arrow-left-filled'
	width={width}
	height={height}
	viewBox='0 0 24 24'
	stroke-width='1.25'
	stroke='currentColor'
	fill='none'
	stroke-linecap='round'
	stroke-linejoin='round'>
	<path stroke='none' d='M0 0h24v24H0z' fill='none'></path>
	<path
		d='M12 2a10 10 0 0 1 .324 19.995l-.324 .005l-.324 -.005a10 10 0 0 1 .324 -19.995zm.707 5.293a1 1 0 0 0 -1.414 0l-4 4a1.048 1.048 0 0 0 -.083 .094l-.064 .092l-.052 .098l-.044 .11l-.03 .112l-.017 .126l-.003 .075l.004 .09l.007 .058l.025 .118l.035 .105l.054 .113l.043 .07l.071 .095l.054 .058l4 4l.094 .083a1 1 0 0 0 1.32 -1.497l-2.292 -2.293h5.585l.117 -.007a1 1 0 0 0 -.117 -1.993h-5.586l2.293 -2.293l.083 -.094a1 1 0 0 0 -.083 -1.32z'
		stroke-width='0'
		fill='currentColor'></path>
</svg>
