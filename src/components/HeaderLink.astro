---
import type { HTMLAttributes } from 'astro/types'
import { cn } from '@/utils'
type Props = HTMLAttributes<'a'>

const { href, class: className, ...props } = Astro.props

const { pathname } = Astro.url
const isActive = href === pathname || href === pathname.replace(/\/$/, '')
---

<a
	href={href}
	class={cn(isActive ? 'text-opacity-100' : 'text-opacity-60', className)}
	rel='noopener noreferrer '
	{...props}
>
	<slot />
</a>
